using API.PDV.Domain;
using API.PDV.Domain.Repositories;
using API.PDV.Application.NicheRules;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace API.PDV.Application
{
    public class ProdutoService
    {
        private readonly IProdutoRepository _produtoRepository;
        private readonly IRepository<Nicho> _nichoRepository;
        private readonly Func<string, INicheRuleService> _nicheRuleServiceFactory;
        private readonly ILogger<ProdutoService> _logger;

        public ProdutoService(
            IProdutoRepository produtoRepository,
            IRepository<Nicho> nichoRepository,
            Func<string, INicheRuleService> nicheRuleServiceFactory,
            ILogger<ProdutoService> logger)
        {
            _produtoRepository = produtoRepository;
            _nichoRepository = nichoRepository;
            _nicheRuleServiceFactory = nicheRuleServiceFactory;
            _logger = logger;
        }

        public async Task<Produto> GetProdutoByIdAsync(Guid id)
        {
            return await _produtoRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<Produto>> GetAllProdutosAsync()
        {
            return await _produtoRepository.GetAllAsync();
        }

        public async Task AddProdutoAsync(Produto produto)
        {
            var nicho = await _nichoRepository.GetByIdAsync(produto.NichoId);
            if (nicho != null)
            {
                var nicheRuleService = _nicheRuleServiceFactory(nicho.Nome);
                await nicheRuleService.ApplyProductRules(produto);
            }
            await _produtoRepository.AddAsync(produto);
            _logger.LogInformation("Produto {ProductName} added successfully.", produto.Nome);
        }

        public async Task UpdateProdutoAsync(Produto produto)
        {
            var nicho = await _nichoRepository.GetByIdAsync(produto.NichoId);
            if (nicho != null)
            {
                var nicheRuleService = _nicheRuleServiceFactory(nicho.Nome);
                await nicheRuleService.ApplyProductRules(produto);
            }
            await _produtoRepository.UpdateAsync(produto);
            _logger.LogInformation("Produto {ProductName} updated successfully.", produto.Nome);
        }

        public async Task DeleteProdutoAsync(Guid id)
        {
            await _produtoRepository.DeleteAsync(id);
            _logger.LogInformation("Produto with ID {ProductId} deleted successfully.", id);
        }
    }
}
