using API.PDV.Application;
using API.PDV.Domain;
using API.PDV.Domain.Repositories;
using API.PDV.Application.NicheRules;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Threading.Tasks;
using Xunit;
using API.PDV.Domain.ValueObjects;

namespace API.PDV.Tests.Unit
{
    public class ProdutoServiceTests
    {
        private readonly Mock<IProdutoRepository> _mockProdutoRepository;
        private readonly Mock<IRepository<Nicho>> _mockNichoRepository;
        private readonly Mock<Func<string, INicheRuleService>> _mockNicheRuleServiceFactory;
        private readonly Mock<ILogger<ProdutoService>> _mockLogger;
        private readonly ProdutoService _produtoService;

        public ProdutoServiceTests()
        {
            _mockProdutoRepository = new Mock<IProdutoRepository>();
            _mockNichoRepository = new Mock<IRepository<Nicho>>();
            _mockNicheRuleServiceFactory = new Mock<Func<string, INicheRuleService>>();
            _mockLogger = new Mock<ILogger<ProdutoService>>();

            _produtoService = new ProdutoService(
                _mockProdutoRepository.Object,
                _mockNichoRepository.Object,
                _mockNicheRuleServiceFactory.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task AddProdutoAsync_ShouldApplyNicheRules_WhenNichoExists()
        {
            // Arrange
            var produto = new Produto
            {
                Id = Guid.NewGuid(),
                Nome = "Test Product",
                NichoId = Guid.NewGuid(),
                Preco = new Preco(10, "USD"),
                EmpresaId = Guid.NewGuid()
            };
            var nicho = new Nicho { Id = produto.NichoId, Nome = "Mercado" };
            var mockNicheRuleService = new Mock<INicheRuleService>();

            _mockNichoRepository.Setup(repo => repo.GetByIdAsync(produto.NichoId))
                .ReturnsAsync(nicho);
            _mockNicheRuleServiceFactory.Setup(factory => factory(nicho.Nome))
                .Returns(mockNicheRuleService.Object);

            // Act
            await _produtoService.AddProdutoAsync(produto);

            // Assert
            mockNicheRuleService.Verify(service => service.ApplyProductRules(produto), Times.Once);
            _mockProdutoRepository.Verify(repo => repo.AddAsync(produto), Times.Once);
        }

        [Fact]
        public async Task AddProdutoAsync_ShouldNotApplyNicheRules_WhenNichoDoesNotExist()
        {
            // Arrange
            var produto = new Produto
            {
                Id = Guid.NewGuid(),
                Nome = "Test Product",
                NichoId = Guid.NewGuid(),
                Preco = new Preco(10, "USD"),
                EmpresaId = Guid.NewGuid()
            };

            _mockNichoRepository.Setup(repo => repo.GetByIdAsync(produto.NichoId))
                .ReturnsAsync((Nicho)null);

            // Act
            await _produtoService.AddProdutoAsync(produto);

            // Assert
            _mockNicheRuleServiceFactory.Verify(factory => factory(It.IsAny<string>()), Times.Never);
            _mockProdutoRepository.Verify(repo => repo.AddAsync(produto), Times.Once);
        }
    }
}
