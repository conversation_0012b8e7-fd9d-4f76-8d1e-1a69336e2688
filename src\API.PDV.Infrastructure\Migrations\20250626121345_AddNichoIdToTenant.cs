﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace API.PDV.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddNichoIdToTenant : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "NichoId",
                schema: "tenant_design_time",
                table: "Tenants",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NichoId",
                schema: "tenant_design_time",
                table: "Tenants");
        }
    }
}
