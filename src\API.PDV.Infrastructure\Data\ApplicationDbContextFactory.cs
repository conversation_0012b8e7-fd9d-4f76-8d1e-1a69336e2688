using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using API.PDV.Application.TenantManagement;

namespace API.PDV.Infrastructure.Data
{
    public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
    {
        public ApplicationDbContext CreateDbContext(string[] args)
        {
            // Build configuration
            // Configure DbContextOptions with a hardcoded connection string for design time
            var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
            optionsBuilder.UseNpgsql("Host=vps.luansilva.com.br;Username=postgres;Password=********************;Database=pdv");

            // Provide a dummy TenantService for design time
            var tenantService = new TenantService();
            tenantService.SetTenantId("design_time"); // Set a dummy tenant ID

            return new ApplicationDbContext(optionsBuilder.Options, tenantService);
        }
    }
}
