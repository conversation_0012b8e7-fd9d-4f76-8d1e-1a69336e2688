using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace API.PDV.Tests.Integration
{
    public class TenantRegistrationTests
    {
        [Theory]
        [InlineData("Tenant A", "tenant_a", "00000000-0000-0000-0000-000000000001")]
        [InlineData("Tenant B", "tenant_b", "00000000-0000-0000-0000-000000000002")]
        public async Task RegisterTenant_ShouldCreateSchemaAndTables(string name, string slug, string nichoId)
        {
            using var client = new HttpClient();
            var json = $"{{\"name\":\"{name}\",\"slug\":\"{slug}\",\"nichoId\":\"{nichoId}\"}}";
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync("http://localhost:5292/api/Tenant/register", content);

            response.EnsureSuccessStatusCode();
        }
    }
}
