using API.PDV.Application.DTOs;
using API.PDV.Application.TenantManagement;
using Dapper;
using Microsoft.Extensions.Configuration;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace API.PDV.Application
{
    public class ReportService
    {
        private readonly IConfiguration _configuration;
        private readonly ITenantService _tenantService;

        public ReportService(IConfiguration configuration, ITenantService tenantService)
        {
            _configuration = configuration;
            _tenantService = tenantService;
        }

        private NpgsqlConnection GetOpenConnection()
        {
            var connectionString = _configuration.GetConnectionString("DefaultConnection");
            var connection = new NpgsqlConnection(connectionString);
            connection.Open();
            return connection;
        }

        public async Task<IEnumerable<VendaReportDto>> GetVendasPorPeriodo(DateTime startDate, DateTime endDate)
        {
            var schema = $"tenant_{_tenantService.GetTenantId()}";
            var sql = $"SELECT v.Id as VendaId, v.DataVenda, v.Total, e.NomeFantasia as EmpresaNome FROM \"{schema}\".\"Vendas\" v JOIN \"{schema}\".\"Empresas\" e ON v.EmpresaId = e.Id WHERE v.DataVenda BETWEEN @StartDate AND @EndDate";

            using (var connection = GetOpenConnection())
            {
                return await connection.QueryAsync<VendaReportDto>(sql, new { StartDate = startDate, EndDate = endDate });
            }
        }

        public async Task<IEnumerable<EstoqueReportDto>> GetEstoqueAtual()
        {
            var schema = $"tenant_{_tenantService.GetTenantId()}";
            var sql = $"SELECT s.ProdutoId, p.Nome as ProdutoNome, s.Quantidade, s.UltimaAtualizacao, e.NomeFantasia as EmpresaNome FROM \"{schema}\".\"Estoques\" s JOIN \"{schema}\".\"Produtos\" p ON s.ProdutoId = p.Id JOIN \"{schema}\".\"Empresas\" e ON s.EmpresaId = e.Id";

            using (var connection = GetOpenConnection())
            {
                return await connection.QueryAsync<EstoqueReportDto>(sql);
            }
        }

        public async Task<IEnumerable<ProdutoMaisVendidoDto>> GetProdutosMaisVendidos(DateTime startDate, DateTime endDate, int topN = 10)
        {
            var schema = $"tenant_{_tenantService.GetTenantId()}";
            var sql = $"SELECT iv.ProdutoId, p.Nome as ProdutoNome, SUM(iv.Quantidade) as TotalVendido FROM \"{schema}\".\"ItensVenda\" iv JOIN \"{schema}\".\"Vendas\" v ON iv.VendaId = v.Id JOIN \"{schema}\".\"Produtos\" p ON iv.ProdutoId = p.Id WHERE v.DataVenda BETWEEN @StartDate AND @EndDate GROUP BY iv.ProdutoId, p.Nome ORDER BY TotalVendido DESC LIMIT @TopN";

            using (var connection = GetOpenConnection())
            {
                return await connection.QueryAsync<ProdutoMaisVendidoDto>(sql, new { StartDate = startDate, EndDate = endDate, TopN = topN });
            }
        }
    }
}