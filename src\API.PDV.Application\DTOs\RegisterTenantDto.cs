using System.ComponentModel.DataAnnotations;
using System;

namespace API.PDV.Application.DTOs
{
    public class RegisterTenantDto
    {
        [Required(ErrorMessage = "Tenant name is required.")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Tenant name must be between 3 and 100 characters.")]
        public string Name { get; set; }

        [Required(ErrorMessage = "Tenant slug is required.")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "Tenant slug must be between 3 and 50 characters.")]
        public string Slug { get; set; }

        [Required(ErrorMessage = "Niche ID is required.")]
        public Guid NichoId { get; set; }
    }
}
