namespace API.PDV.Domain.ValueObjects
{
    public record Endereco
    {
        public string Logradouro { get; init; }
        public string Numero { get; init; }
        public string Complemento { get; init; }
        public string <PERSON>rro { get; init; }
        public string Cidade { get; init; }
        public string Estado { get; init; }
        public string Cep { get; init; }

        public Endereco(string logradouro, string numero, string complemento, string bairro, string cidade, string estado, string cep)
        {
            Logradouro = logradouro;
            Numero = numero;
            Complemento = complemento;
            Bairro = bairro;
            Cidade = cidade;
            Estado = estado;
            Cep = cep;
        }
    }
}
