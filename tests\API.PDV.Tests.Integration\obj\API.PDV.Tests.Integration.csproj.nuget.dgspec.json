{"format": 1, "restore": {"D:\\API.PDV\\tests\\API.PDV.Tests.Integration\\API.PDV.Tests.Integration.csproj": {}}, "projects": {"D:\\API.PDV\\src\\API.PDV.API\\API.PDV.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\API.PDV\\src\\API.PDV.API\\API.PDV.API.csproj", "projectName": "API.PDV.API", "projectPath": "D:\\API.PDV\\src\\API.PDV.API\\API.PDV.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\API.PDV\\src\\API.PDV.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj"}, "D:\\API.PDV\\src\\API.PDV.Infrastructure\\API.PDV.Infrastructure.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.Infrastructure\\API.PDV.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}, "D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj", "projectName": "API.PDV.Application", "projectPath": "D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\API.PDV\\src\\API.PDV.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.66, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Npgsql": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}, "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj", "projectName": "API.PDV.Domain", "projectPath": "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\API.PDV\\src\\API.PDV.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}, "D:\\API.PDV\\src\\API.PDV.Infrastructure\\API.PDV.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\API.PDV\\src\\API.PDV.Infrastructure\\API.PDV.Infrastructure.csproj", "projectName": "API.PDV.Infrastructure", "projectPath": "D:\\API.PDV\\src\\API.PDV.Infrastructure\\API.PDV.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\API.PDV\\src\\API.PDV.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.66, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.6, )"}, "Npgsql": {"target": "Package", "version": "[9.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}, "D:\\API.PDV\\tests\\API.PDV.Tests.Integration\\API.PDV.Tests.Integration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\API.PDV\\tests\\API.PDV.Tests.Integration\\API.PDV.Tests.Integration.csproj", "projectName": "API.PDV.Tests.Integration", "projectPath": "D:\\API.PDV\\tests\\API.PDV.Tests.Integration\\API.PDV.Tests.Integration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\API.PDV\\tests\\API.PDV.Tests.Integration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\API.PDV\\src\\API.PDV.API\\API.PDV.API.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.API\\API.PDV.API.csproj"}, "D:\\API.PDV\\src\\API.PDV.Infrastructure\\API.PDV.Infrastructure.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.Infrastructure\\API.PDV.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.5.3, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}