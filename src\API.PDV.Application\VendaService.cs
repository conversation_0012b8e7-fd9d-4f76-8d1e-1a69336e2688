using API.PDV.Domain;
using API.PDV.Domain.Repositories;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace API.PDV.Application
{
    public class VendaService
    {
        private readonly IVendaRepository _vendaRepository;
        private readonly IItemVendaRepository _itemVendaRepository;
        private readonly IPagamentoRepository _pagamentoRepository;
        private readonly IProdutoRepository _produtoRepository;
        private readonly IEstoqueRepository _estoqueRepository;
        private readonly ILogger<VendaService> _logger;

        public VendaService(
            IVendaRepository vendaRepository,
            IItemVendaRepository itemVendaRepository,
            IPagamentoRepository pagamentoRepository,
            IProdutoRepository produtoRepository,
            IEstoqueRepository estoqueRepository,
            ILogger<VendaService> logger)
        {
            _vendaRepository = vendaRepository;
            _itemVendaRepository = itemVendaRepository;
            _pagamentoRepository = pagamentoRepository;
            _produtoRepository = produtoRepository;
            _estoqueRepository = estoqueRepository;
            _logger = logger;
        }

        public async Task<Venda> IniciarVenda(Guid empresaId)
        {
            var venda = new Venda
            {
                Id = Guid.NewGuid(),
                DataVenda = DateTime.UtcNow,
                EmpresaId = empresaId,
                ItensVenda = new List<ItemVenda>(),
                Pagamentos = new List<Pagamento>()
            };
            await _vendaRepository.AddAsync(venda);
            _logger.LogInformation("Venda {VendaId} iniciada para a empresa {EmpresaId}", venda.Id, empresaId);
            return venda;
        }

        public async Task<Venda> AdicionarItemVenda(Guid vendaId, Guid produtoId, int quantidade)
        {
            var venda = await _vendaRepository.GetByIdAsync(vendaId);
            if (venda == null) throw new ArgumentException("Venda não encontrada.");

            var produto = await _produtoRepository.GetByIdAsync(produtoId);
            if (produto == null) throw new ArgumentException("Produto não encontrado.");

            var estoque = (await _estoqueRepository.GetAllAsync()).FirstOrDefault(e => e.ProdutoId == produtoId);
            if (estoque == null || estoque.Quantidade < quantidade) throw new InvalidOperationException("Estoque insuficiente.");

            var itemVenda = new ItemVenda
            {
                Id = Guid.NewGuid(),
                VendaId = vendaId,
                ProdutoId = produtoId,
                Quantidade = quantidade,
                PrecoUnitario = produto.Preco.Valor,
                Subtotal = produto.Preco.Valor * quantidade
            };

            await _itemVendaRepository.AddAsync(itemVenda);
            venda.ItensVenda.Add(itemVenda);
            venda.Total = venda.ItensVenda.Sum(item => item.Subtotal);
            await _vendaRepository.UpdateAsync(venda);

            // Baixa no estoque
            estoque.Quantidade -= quantidade;
            await _estoqueRepository.UpdateAsync(estoque);

            _logger.LogInformation("Item {ProductName} ({Quantity}) adicionado à venda {VendaId}", produto.Nome, quantidade, vendaId);
            return venda;
        }

        public async Task<Venda> FecharVenda(Guid vendaId, decimal valorRecebido, string formaPagamento)
        {
            var venda = await _vendaRepository.GetByIdAsync(vendaId);
            if (venda == null) throw new ArgumentException("Venda não encontrada.");

            var pagamento = new Pagamento
            {
                Id = Guid.NewGuid(),
                VendaId = vendaId,
                ValorPago = valorRecebido,
                FormaPagamento = formaPagamento,
                DataPagamento = DateTime.UtcNow
            };

            await _pagamentoRepository.AddAsync(pagamento);
            venda.Pagamentos.Add(pagamento);

            venda.Troco = valorRecebido - venda.Total;
            await _vendaRepository.UpdateAsync(venda);

            _logger.LogInformation("Venda {VendaId} fechada. Total: {Total}, Recebido: {Received}, Troco: {Troco}", venda.Id, venda.Total, valorRecebido, venda.Troco);
            return venda;
        }
    }
}
