using System;
using System.ComponentModel.DataAnnotations;

namespace API.PDV.Application.DTOs
{
    public class CloseSaleDto
    {
        [Required]
        public Guid VendaId { get; set; }

        [Range(0.01, double.MaxValue, ErrorMessage = "Received value must be greater than 0.")]
        public decimal ValorRecebido { get; set; }

        [Required(ErrorMessage = "Payment method is required.")]
        public string FormaPagamento { get; set; }
    }
}
