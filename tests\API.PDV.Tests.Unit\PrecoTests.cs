using API.PDV.Domain.ValueObjects;
using Xunit;
using System;

namespace API.PDV.Tests.Unit
{
    public class PrecoTests
    {
        [Fact]
        public void Preco_ShouldNotAllowNegativeValue()
        {
            Assert.Throws<ArgumentException>(() => new Preco(-10, "BRL"));
        }

        [Fact]
        public void Preco_ShouldNotAllowEmptyCurrency()
        {
            Assert.Throws<ArgumentException>(() => new Preco(10, ""));
        }

        [Fact]
        public void Preco_ShouldBeCreatedWithValidValues()
        {
            var preco = new Preco(100, "USD");
            Assert.Equal(100, preco.Valor);
            Assert.Equal("USD", preco.Moeda);
        }
    }
}
