using System;
using System.Collections.Generic;

namespace API.PDV.Domain
{
    public class Venda : BaseEntity
    {
        public DateTime DataVenda { get; set; }
        public decimal Total { get; set; }
        public decimal Troco { get; set; }
        public Guid EmpresaId { get; set; }
        public Empresa Empresa { get; set; }
        public ICollection<ItemVenda> ItensVenda { get; set; }
        public ICollection<Pagamento> Pagamentos { get; set; }
    }
}
