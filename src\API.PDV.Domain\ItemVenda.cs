using System;

namespace API.PDV.Domain
{
    public class ItemVenda : BaseEntity
    {
        public Guid VendaId { get; set; }
        public Venda Venda { get; set; }
        public Guid ProdutoId { get; set; }
        public Produto Produto { get; set; }
        public int Quantidade { get; set; }
        public decimal PrecoUnitario { get; set; }
        public decimal Subtotal { get; set; }
    }
}
