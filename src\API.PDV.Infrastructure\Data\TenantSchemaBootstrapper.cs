using API.PDV.Application.TenantManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace API.PDV.Infrastructure.Data
{
    public class TenantSchemaBootstrapper : ITenantSchemaBootstrapper
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<TenantSchemaBootstrapper> _logger;

        public TenantSchemaBootstrapper(IConfiguration configuration, ILogger<TenantSchemaBootstrapper> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task BootstrapSchemaAsync(string tenantSlug)
        {
            if (string.IsNullOrWhiteSpace(tenantSlug))
                throw new ArgumentException("O slug do tenant não pode ser vazio ou nulo.", nameof(tenantSlug));

            var connectionString = _configuration.GetConnectionString("DefaultConnection");
            var schemaName = $"tenant_{tenantSlug}";

            var tenantService = new TenantService();
            tenantService.SetTenantId(tenantSlug);

            var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
            optionsBuilder.UseNpgsql(connectionString);
            _logger.LogInformation("Applying migrations for schema {SchemaName}", schemaName);
            _logger.LogInformation("Current TenantId: {TenantId}", tenantService.GetTenantId());

            using (var context = new ApplicationDbContext(optionsBuilder.Options, tenantService))
            {
                _logger.LogInformation("Applying migrations for schema {SchemaName}", schemaName);
                await context.Database.MigrateAsync();
                _logger.LogInformation("Migrations applied for schema {SchemaName}", schemaName);
            }
        }
    }
}
