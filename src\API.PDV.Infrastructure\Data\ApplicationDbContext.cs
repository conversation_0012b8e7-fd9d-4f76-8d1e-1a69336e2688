using Microsoft.EntityFrameworkCore;
using API.PDV.Domain;
using API.PDV.Application.TenantManagement;

namespace API.PDV.Infrastructure.Data
{
    public class ApplicationDbContext : DbContext
    {
        private readonly ITenantService _tenantService;

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, ITenantService tenantService) : base(options)
        {
            _tenantService = tenantService;
        }

        public DbSet<Tenant> Tenants { get; set; }
        public DbSet<Empresa> Empresas { get; set; }
        public DbSet<Nicho> Nichos { get; set; }
        public DbSet<Configuracao> Configuracoes { get; set; }
        public DbSet<Produto> Produtos { get; set; }
        public DbSet<Estoque> Estoques { get; set; }
        public DbSet<Venda> Vendas { get; set; }
        public DbSet<ItemVenda> ItensVenda { get; set; }
        public DbSet<Pagamento> Pagamentos { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.ConfigureWarnings(warnings => warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.PendingModelChangesWarning));
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.HasDefaultSchema($"tenant_{_tenantService.GetTenantId()}");

            modelBuilder.Entity<Tenant>().HasKey(t => t.Id);
            modelBuilder.Entity<Tenant>().Property(t => t.Name).IsRequired();
            modelBuilder.Entity<Tenant>().Property(t => t.Slug).IsRequired();
            modelBuilder.Entity<Tenant>().Property(t => t.NichoId).IsRequired();

            modelBuilder.Entity<Produto>().OwnsOne(p => p.Preco);

            // TODO: Add more entity configurations here
        }
    }
}
