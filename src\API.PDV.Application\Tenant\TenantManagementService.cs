using API.PDV.Domain;
using API.PDV.Domain.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using API.PDV.Application.NicheRules;

namespace API.PDV.Application.TenantManagement
{
    public class TenantManagementService
    {
        private readonly IRepository<Tenant> _tenantRepository;
        private readonly IRepository<Nicho> _nichoRepository;
        private readonly IRepository<Empresa> _empresaRepository;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TenantManagementService> _logger;
        private readonly ITenantSchemaBootstrapper _tenantSchemaBootstrapper;
        private readonly Func<string, INicheRuleService> _nicheRuleServiceFactory;

        public TenantManagementService(
            IRepository<Tenant> tenantRepository,
            IRepository<Nicho> nichoRepository,
            IRepository<Empresa> empresaRepository,
            IConfiguration configuration,
            ILogger<TenantManagementService> logger,
            ITenantSchemaBootstrapper tenantSchemaBootstrapper,
            Func<string, INicheRuleService> nicheRuleServiceFactory)
        {
            _tenantRepository = tenantRepository;
            _nichoRepository = nichoRepository;
            _empresaRepository = empresaRepository;
            _configuration = configuration;
            _logger = logger;
            _tenantSchemaBootstrapper = tenantSchemaBootstrapper;
            _nicheRuleServiceFactory = nicheRuleServiceFactory;
        }

        public async Task<Tenant> RegisterTenantAsync(string name, string slug, Guid nichoId)
        {
            var tenant = new Tenant
            {
                Id = Guid.NewGuid(),
                Name = name,
                Slug = slug,
                ConnectionString = _configuration.GetConnectionString("DefaultConnection") // Use default connection string for now
            };

            // Bootstrap schema for the new tenant
            await _tenantSchemaBootstrapper.BootstrapSchemaAsync(tenant.Slug);

            await _tenantRepository.AddAsync(tenant);
            _logger.LogInformation("Tenant {TenantName} registered successfully.", name);

            // Create a default company for the tenant
            var empresa = new Empresa
            {
                Id = Guid.NewGuid(),
                RazaoSocial = name,
                NomeFantasia = name,
                TenantId = tenant.Id,
                // TODO: Populate other Empresa properties
            };
            await _empresaRepository.AddAsync(empresa);

            // Apply niche-specific rules
            var nicho = await _nichoRepository.GetByIdAsync(nichoId);
            if (nicho != null)
            {
                var nicheRuleService = _nicheRuleServiceFactory(nicho.Nome);
                await nicheRuleService.ApplyBusinessRules(empresa);
                await nicheRuleService.ApplyRestrictionRules(empresa);
            }

            return tenant;
        }
    }
}
