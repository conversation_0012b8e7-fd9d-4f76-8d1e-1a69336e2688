﻿// <auto-generated />
using System;
using API.PDV.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace API.PDV.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250626125335_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("tenant_design_time")
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("API.PDV.Domain.Configuracao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Chave")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("NichoId")
                        .HasColumnType("uuid");

                    b.Property<string>("Valor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("NichoId");

                    b.ToTable("Configuracoes", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.Empresa", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Endereco")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("NomeFantasia")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RazaoSocial")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Telefone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Empresas", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.Estoque", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid");

                    b.Property<int>("Quantidade")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UltimaAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ProdutoId");

                    b.ToTable("Estoques", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.ItemVenda", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("PrecoUnitario")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid");

                    b.Property<int>("Quantidade")
                        .HasColumnType("integer");

                    b.Property<decimal>("Subtotal")
                        .HasColumnType("numeric");

                    b.Property<Guid>("VendaId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProdutoId");

                    b.HasIndex("VendaId");

                    b.ToTable("ItensVenda", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.Nicho", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Nichos", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.Pagamento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DataPagamento")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FormaPagamento")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("ValorPago")
                        .HasColumnType("numeric");

                    b.Property<Guid>("VendaId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("VendaId");

                    b.ToTable("Pagamentos", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.Produto", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("NichoId")
                        .HasColumnType("uuid");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("NichoId");

                    b.ToTable("Produtos", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConnectionString")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("NichoId")
                        .HasColumnType("uuid");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Tenants", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.Venda", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DataVenda")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Total")
                        .HasColumnType("numeric");

                    b.Property<decimal>("Troco")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("EmpresaId");

                    b.ToTable("Vendas", "tenant_design_time");
                });

            modelBuilder.Entity("API.PDV.Domain.Configuracao", b =>
                {
                    b.HasOne("API.PDV.Domain.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("API.PDV.Domain.Nicho", "Nicho")
                        .WithMany()
                        .HasForeignKey("NichoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Empresa");

                    b.Navigation("Nicho");
                });

            modelBuilder.Entity("API.PDV.Domain.Estoque", b =>
                {
                    b.HasOne("API.PDV.Domain.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("API.PDV.Domain.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Empresa");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("API.PDV.Domain.ItemVenda", b =>
                {
                    b.HasOne("API.PDV.Domain.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("API.PDV.Domain.Venda", "Venda")
                        .WithMany("ItensVenda")
                        .HasForeignKey("VendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Produto");

                    b.Navigation("Venda");
                });

            modelBuilder.Entity("API.PDV.Domain.Pagamento", b =>
                {
                    b.HasOne("API.PDV.Domain.Venda", "Venda")
                        .WithMany("Pagamentos")
                        .HasForeignKey("VendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Venda");
                });

            modelBuilder.Entity("API.PDV.Domain.Produto", b =>
                {
                    b.HasOne("API.PDV.Domain.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("API.PDV.Domain.Nicho", "Nicho")
                        .WithMany()
                        .HasForeignKey("NichoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("API.PDV.Domain.ValueObjects.Preco", "Preco", b1 =>
                        {
                            b1.Property<Guid>("ProdutoId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Moeda")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.Property<decimal>("Valor")
                                .HasColumnType("numeric");

                            b1.HasKey("ProdutoId");

                            b1.ToTable("Produtos", "tenant_design_time");

                            b1.WithOwner()
                                .HasForeignKey("ProdutoId");
                        });

                    b.Navigation("Empresa");

                    b.Navigation("Nicho");

                    b.Navigation("Preco")
                        .IsRequired();
                });

            modelBuilder.Entity("API.PDV.Domain.Venda", b =>
                {
                    b.HasOne("API.PDV.Domain.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Empresa");
                });

            modelBuilder.Entity("API.PDV.Domain.Venda", b =>
                {
                    b.Navigation("ItensVenda");

                    b.Navigation("Pagamentos");
                });
#pragma warning restore 612, 618
        }
    }
}
