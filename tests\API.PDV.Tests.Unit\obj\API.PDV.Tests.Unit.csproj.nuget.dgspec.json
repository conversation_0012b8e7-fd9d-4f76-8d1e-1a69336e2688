{"format": 1, "restore": {"D:\\API.PDV\\tests\\API.PDV.Tests.Unit\\API.PDV.Tests.Unit.csproj": {}}, "projects": {"D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj", "projectName": "API.PDV.Application", "projectPath": "D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\API.PDV\\src\\API.PDV.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.66, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Npgsql": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}, "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj", "projectName": "API.PDV.Domain", "projectPath": "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\API.PDV\\src\\API.PDV.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}, "D:\\API.PDV\\tests\\API.PDV.Tests.Unit\\API.PDV.Tests.Unit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\API.PDV\\tests\\API.PDV.Tests.Unit\\API.PDV.Tests.Unit.csproj", "projectName": "API.PDV.Tests.Unit", "projectPath": "D:\\API.PDV\\tests\\API.PDV.Tests.Unit\\API.PDV.Tests.Unit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\API.PDV\\tests\\API.PDV.Tests.Unit\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.Application\\API.PDV.Application.csproj"}, "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj": {"projectPath": "D:\\API.PDV\\src\\API.PDV.Domain\\API.PDV.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.5.3, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}