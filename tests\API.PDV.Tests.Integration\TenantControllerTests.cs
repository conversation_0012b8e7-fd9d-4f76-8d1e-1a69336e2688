using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Net;
using System;

namespace API.PDV.Tests.Integration
{
    public class TenantControllerTests : IntegrationTestBase
    {
        public TenantControllerTests(WebApplicationFactory<Program> factory) : base(factory)
        {
        }

        [Fact]
        public async Task RegisterTenant_ReturnsOk_WithValidData()
        {
            // Arrange
            var client = _factory.CreateClient();
            var tenantName = "TestCompany";
            var tenantSlug = "testcompany";
            var nichoId = Guid.NewGuid(); // In a real scenario, this would be an existing niche ID

            // Act
            var response = await client.PostAsync($"/api/Tenant/register?name={tenantName}&slug={tenantSlug}&nichoId={nichoId}", null);

            // Assert
            response.EnsureSuccessStatusCode(); // Status Code 200-299
            var responseString = await response.Content.ReadAsStringAsync();
            Assert.Contains(tenantName, responseString);
            Assert.Contains(tenantSlug, responseString);
        }

        [Fact]
        public async Task RegisterTenant_ReturnsBadRequest_WithMissingData()
        {
            // Arrange
            var client = _factory.CreateClient();

            // Act (missing slug and nichoId)
            var response = await client.PostAsync($"/api/Tenant/register?name=InvalidTenant", null);

            // Assert
            Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        }
    }
}
