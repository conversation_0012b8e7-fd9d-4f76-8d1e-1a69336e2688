using API.PDV.Domain;
using API.PDV.Domain.Repositories;
using Microsoft.EntityFrameworkCore;

namespace API.PDV.Infrastructure.Data.Repositories
{
    public class TenantRepository : EfRepository<Tenant>, IRepository<Tenant>
    {
        public TenantRepository(ApplicationDbContext dbContext) : base(dbContext)
        {
        }

        public async Task<Tenant> GetBySlugAsync(string slug)
        {
            return await _dbContext.Set<Tenant>().FirstOrDefaultAsync(t => t.Slug == slug);
        }
    }
}
