namespace API.PDV.Domain.ValueObjects
{
    public record Preco
    {
        public decimal Valor { get; init; }
        public string Moeda { get; init; }

        public Preco(decimal valor, string moeda)
        {
            if (valor < 0)
            {
                throw new ArgumentException("Price cannot be negative.", nameof(valor));
            }
            if (string.IsNullOrWhiteSpace(moeda))
            {
                throw new ArgumentException("Currency cannot be empty.", nameof(moeda));
            }
            Valor = valor;
            Moeda = moeda;
        }
    }
}
