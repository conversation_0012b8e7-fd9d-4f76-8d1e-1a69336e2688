﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace API.PDV.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class SeedNiches : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Nicho<PERSON>",
                columns: new[] { "Id", "Nome", "Descricao" },
                values: new object[] { Guid.NewGuid(), "Mercado", "Estoque segmentado por departamentos, Preço por unidade ou peso, Promoções com validade, Controle de datas de validade por item" });

            migrationBuilder.InsertData(
                table: "Nichos",
                columns: new[] { "Id", "Nome", "Descricao" },
                values: new object[] { Guid.NewGuid(), "Distribuidora de Bebidas", "Vendas por caixa ou litros, Preços especiais para revenda, Entregas programadas com rotas, Controle de temperatura por lote" });

            migrationBuilder.InsertData(
                table: "Nichos",
                columns: new[] { "Id", "Nome", "Descricao" },
                values: new object[] { Guid.NewGuid(), "Feira Livre", "Vendas por peso (balança), Preço livre (flexível por produto e feirante), Estoque semanal, Localização variável (feira itinerante)" });

            migrationBuilder.InsertData(
                table: "Nichos",
                columns: new[] { "Id", "Nome", "Descricao" },
                values: new object[] { Guid.NewGuid(), "Padrão", "Todo nicho herda o núcleo funcional e pode customizar fluxos" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Nichos",
                keyColumn: "Nome",
                keyValue: "Mercado");

            migrationBuilder.DeleteData(
                table: "Nichos",
                keyColumn: "Nome",
                keyValue: "Distribuidora de Bebidas");

            migrationBuilder.DeleteData(
                table: "Nichos",
                keyColumn: "Nome",
                keyValue: "Feira Livre");

            migrationBuilder.DeleteData(
                table: "Nichos",
                keyColumn: "Nome",
                keyValue: "Padrão");
        }
    }
}
