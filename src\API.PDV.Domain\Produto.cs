using API.PDV.Domain.ValueObjects;
using System;

namespace API.PDV.Domain
{
    public class Produto : BaseEntity
    {
        public string Nome { get; set; }
        public string Descricao { get; set; }
        public Preco Preco { get; set; }
        public Guid NichoId { get; set; }
        public Nicho Nicho { get; set; }
        public Guid EmpresaId { get; set; }
        public Empresa Empresa { get; set; }
    }
}
