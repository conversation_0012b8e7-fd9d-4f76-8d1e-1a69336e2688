using API.PDV.Domain.Repositories;
using Npgsql;
using Dapper;
using Microsoft.Extensions.Configuration;
using API.PDV.Domain;

namespace API.PDV.Infrastructure.Data.Repositories
{
    public class DapperRepository<TEntity> : IRepository<TEntity>
        where TEntity : BaseEntity
    {
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;

        public DapperRepository(IConfiguration configuration)
        {
            _configuration = configuration;
            _connectionString = _configuration.GetConnectionString("DefaultConnection");
        }

        public async Task<TEntity> GetByIdAsync(Guid id)
        {
            using var connection = new NpgsqlConnection(_connectionString);
            var sql = $"SELECT * FROM {typeof(TEntity).Name}s WHERE Id = @Id";
            return await connection.QuerySingleOrDefaultAsync<TEntity>(sql, new { Id = id });
        }

        public async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            using var connection = new NpgsqlConnection(_connectionString);
            var sql = $"SELECT * FROM {typeof(TEntity).Name}s";
            return await connection.QueryAsync<TEntity>(sql);
        }

        public async Task AddAsync(TEntity entity)
        {
            using var connection = new NpgsqlConnection(_connectionString);
            var properties = typeof(TEntity).GetProperties().Where(p => p.Name != "Id");
            var columnNames = string.Join(", ", properties.Select(p => p.Name));
            var parameterNames = string.Join(", ", properties.Select(p => "@" + p.Name));
            var sql = $"INSERT INTO {typeof(TEntity).Name}s ({columnNames}) VALUES ({parameterNames})";
            await connection.ExecuteAsync(sql, entity);
        }

        public async Task UpdateAsync(TEntity entity)
        {
            using var connection = new NpgsqlConnection(_connectionString);
            var properties = typeof(TEntity).GetProperties().Where(p => p.Name != "Id");
            var setClauses = string.Join(", ", properties.Select(p => $"{p.Name} = @{p.Name}"));
            var sql = $"UPDATE {typeof(TEntity).Name}s SET {setClauses} WHERE Id = @Id";
            await connection.ExecuteAsync(sql, entity);
        }

        public async Task DeleteAsync(Guid id)
        {
            using var connection = new NpgsqlConnection(_connectionString);
            var sql = $"DELETE FROM {typeof(TEntity).Name}s WHERE Id = @Id";
            await connection.ExecuteAsync(sql, new { Id = id });
        }
    }
}
