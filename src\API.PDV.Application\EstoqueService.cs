using API.PDV.Domain;
using API.PDV.Domain.Repositories;
using API.PDV.Application.NicheRules;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace API.PDV.Application
{
    public class EstoqueService
    {
        private readonly IEstoqueRepository _estoqueRepository;
        private readonly IProdutoRepository _produtoRepository;
        private readonly IRepository<Nicho> _nichoRepository;
        private readonly Func<string, INicheRuleService> _nicheRuleServiceFactory;
        private readonly ILogger<EstoqueService> _logger;

        public EstoqueService(
            IEstoqueRepository estoqueRepository,
            IProdutoRepository produtoRepository,
            IRepository<Nicho> nichoRepository,
            Func<string, INicheRuleService> nicheRuleServiceFactory,
            ILogger<EstoqueService> logger)
        {
            _estoqueRepository = estoqueRepository;
            _produtoRepository = produtoRepository;
            _nichoRepository = nichoRepository;
            _nicheRuleServiceFactory = nicheRuleServiceFactory;
            _logger = logger;
        }

        public async Task<Estoque> GetEstoqueByIdAsync(Guid id)
        {
            return await _estoqueRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<Estoque>> GetAllEstoquesAsync()
        {
            return await _estoqueRepository.GetAllAsync();
        }

        public async Task AddEstoqueAsync(Estoque estoque)
        {
            var produto = await _produtoRepository.GetByIdAsync(estoque.ProdutoId);
            if (produto != null)
            {
                var nicho = await _nichoRepository.GetByIdAsync(produto.NichoId);
                if (nicho != null)
                {
                    var nicheRuleService = _nicheRuleServiceFactory(nicho.Nome);
                    await nicheRuleService.ApplyInventoryRules(estoque);
                }
            }
            estoque.UltimaAtualizacao = DateTime.UtcNow;
            await _estoqueRepository.AddAsync(estoque);
            _logger.LogInformation("Estoque for product {ProductId} added successfully.", estoque.ProdutoId);
        }

        public async Task UpdateEstoqueAsync(Estoque estoque)
        {
            var produto = await _produtoRepository.GetByIdAsync(estoque.ProdutoId);
            if (produto != null)
            {
                var nicho = await _nichoRepository.GetByIdAsync(produto.NichoId);
                if (nicho != null)
                {
                    var nicheRuleService = _nicheRuleServiceFactory(nicho.Nome);
                    await nicheRuleService.ApplyInventoryRules(estoque);
                }
            }
            estoque.UltimaAtualizacao = DateTime.UtcNow;
            await _estoqueRepository.UpdateAsync(estoque);
            _logger.LogInformation("Estoque for product {ProductId} updated successfully.", estoque.ProdutoId);
        }

        public async Task DeleteEstoqueAsync(Guid id)
        {
            await _estoqueRepository.DeleteAsync(id);
            _logger.LogInformation("Estoque with ID {EstoqueId} deleted successfully.", id);
        }

        public async Task EntradaEstoque(Guid produtoId, int quantidade)
        {
            var estoque = (await _estoqueRepository.GetAllAsync()).FirstOrDefault(e => e.ProdutoId == produtoId);
            if (estoque != null)
            {
                estoque.Quantidade += quantidade;
                await UpdateEstoqueAsync(estoque);
                _logger.LogInformation("Entrada de {Quantidade} unidades para o produto {ProductId}. Novo estoque: {NewQuantity}", quantidade, produtoId, estoque.Quantidade);
            }
            else
            {
                _logger.LogWarning("Produto {ProductId} não encontrado no estoque para entrada.", produtoId);
            }
        }

        public async Task SaidaEstoque(Guid produtoId, int quantidade)
        {
            var estoque = (await _estoqueRepository.GetAllAsync()).FirstOrDefault(e => e.ProdutoId == produtoId);
            if (estoque != null && estoque.Quantidade >= quantidade)
            {
                estoque.Quantidade -= quantidade;
                await UpdateEstoqueAsync(estoque);
                _logger.LogInformation("Saída de {Quantidade} unidades para o produto {ProductId}. Novo estoque: {NewQuantity}", quantidade, produtoId, estoque.Quantidade);
            }
            else if (estoque != null && estoque.Quantidade < quantidade)
            {
                _logger.LogWarning("Estoque insuficiente para o produto {ProductId}. Quantidade solicitada: {RequestedQuantity}, Estoque disponível: {AvailableQuantity}", produtoId, quantidade, estoque.Quantidade);
            }
            else
            {
                _logger.LogWarning("Produto {ProductId} não encontrado no estoque para saída.", produtoId);
            }
        }
    }
}
