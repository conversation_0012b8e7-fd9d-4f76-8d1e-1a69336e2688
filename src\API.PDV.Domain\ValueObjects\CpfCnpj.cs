namespace API.PDV.Domain.ValueObjects
{
    public record CpfCnpj
    {
        public string Value { get; init; }

        public CpfCnpj(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                throw new ArgumentException("CPF/CNPJ cannot be empty.", nameof(value));
            }
            // TODO: Add CPF/CNPJ validation logic
            Value = value;
        }

        public static implicit operator string(CpfCnpj cpfCnpj) => cpfCnpj.Value;
        public static implicit operator CpfCnpj(string value) => new CpfCnpj(value);
    }
}
