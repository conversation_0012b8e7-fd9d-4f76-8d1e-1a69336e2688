using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using API.PDV.Infrastructure.Data;
using Xunit;
using System.Threading.Tasks;
using System;
using API.PDV.Application.TenantManagement;

namespace API.PDV.Tests.Integration
{
    public class IntegrationTestBase : IClassFixture<WebApplicationFactory<Program>>
    {
        protected readonly WebApplicationFactory<Program> _factory;
        protected readonly string _testTenantId = "test_tenant";

        public IntegrationTestBase(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Remove the app's ApplicationDbContext registration
                    var descriptor = services.SingleOrDefault(
                        d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
                    if (descriptor != null)
                    {
                        services.Remove(descriptor);
                    }

                    // Add ApplicationDbContext using an in-memory database for testing
                    services.AddDbContext<ApplicationDbContext>(options =>
                    {
                        options.UseNpgsql($"Host=localhost;Database={_testTenantId};Username=postgres;Password=********************");
                    });

                    // Ensure ITenantService is registered for the test tenant
                    services.AddScoped<ITenantService>(provider =>
                    {
                        var tenantService = new TenantService();
                        tenantService.SetTenantId(_testTenantId);
                        return tenantService;
                    });

                    var sp = services.BuildServiceProvider();

                    using (var scope = sp.CreateScope())
                    {
                        var scopedServices = scope.ServiceProvider;
                        var db = scopedServices.GetRequiredService<ApplicationDbContext>();
                        var logger = scopedServices.GetRequiredService<Microsoft.Extensions.Logging.ILogger<IntegrationTestBase>>();

                        db.Database.EnsureDeleted();
                        db.Database.Migrate();
                    }
                });
            });
        }

        protected async Task<ApplicationDbContext> GetDbContextAsync()
        {
            var scopeFactory = _factory.Services.GetRequiredService<IServiceScopeFactory>();
            var scope = scopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            return dbContext;
        }
    }
}
